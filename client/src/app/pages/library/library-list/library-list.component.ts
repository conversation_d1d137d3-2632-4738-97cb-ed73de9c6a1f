import { LibraryService } from "@/services/library.service";
import { ProfileService } from "@/services/profile.service";
import {  NgClass, CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common";
import { Component, inject, PLATFORM_ID, DestroyRef, effect, ElementRef, ViewChild, HostListener } from '@angular/core';
import { environment } from "@/env/environment";
import { ActivatedRoute, Router, RouterLink } from "@angular/router";
import { ToasterService } from "@/services/toaster.service";
import { AdvertisingService } from "@/services/advertising.service";
import { TranslocoModule, TranslocoService } from "@jsverse/transloco";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslationService } from "@/services/translation.service";
import { AuthService } from "@/services/auth.service";
import { ShareDataService } from "@/services/share-data.service";
import {debounceTime, distinctUntilChanged, Subject, switchMap, tap} from "rxjs";
import { CustomDropdownComponent } from "@/components/custom-dropdown/custom-dropdown.component";
import { formatContentCountMessage } from '@/utils/pluralization.util';

@Component({
  selector: 'app-library-list',
  standalone: true,
  imports: [
    NgClass,
    CommonModule,
    RouterLink,
    BreadcrumbComponent,
    ReactiveFormsModule,
    FormsModule,
    NgOptimizedImage,
    CustomDropdownComponent,
    TranslocoModule
  ],
  templateUrl: './library-list.component.html',
  styleUrl: './library-list.component.scss'
})
export class LibraryListComponent {
  protected readonly environment = environment;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @HostListener('document:mousedown', ['$event'])
  onClickOutside(event: MouseEvent) {
    const modal = this.modal?.nativeElement as HTMLDialogElement;
    if (modal && modal.open) {
      const dialogContent = modal.querySelector('.cont_mod');
      if (dialogContent && !dialogContent.contains(event.target as Node) &&
        !(event.target as HTMLElement).classList.contains('x_bt')) {
        this.closeModal(modal);
      }
    }
  }
  searchSubject = new Subject<any>();
  libraryService = inject(LibraryService);
  profileService = inject(ProfileService);
  toasterService = inject(ToasterService);
  advertisingService = inject(AdvertisingService);
  fb = inject(FormBuilder)
  isOpened: Record<number, boolean> = {};
  show_menu: Record<number, boolean> = {};
  translocoService = inject(TranslocoService);
  translationService = inject(TranslationService);
  shareDataService = inject(ShareDataService)
  authService = inject(AuthService)
  route = inject(ActivatedRoute);
  router = inject(Router);
  platformId = inject(PLATFORM_ID);
  private readonly destroyRef = inject(DestroyRef);
  filters: any = {
    description: '',
    sortOrder: 'date',
    audio: '',
    paid: false,
    page: 1
  };
  filter: FormGroup = this.fb.group({
    search: '',
    category: '',
    sortOrder: 'date',
    tag: [],
    page: 1,
  })
  lang = 'ru';
  dropdownSortOpen = false;
  selectedSortLabel = 'Алфавиту';
  sortOptions = [
    { label: 'Алфавиту', value: 'title', id: 0 },
    { label: 'Дате', value: 'date', id: 1 },
    { label: 'Популярности', value: 'views', id: 2 }
  ];
  sortDirection: 'Asc' | 'Desc' = 'Asc';
  currentSortField: string = 'title';
  data: any = [];
  selectedTags: any[] = [];
  selectedAuthors: <AUTHORS>
  selectedCategories: any[] = [];

  // Scroll position preservation for pagination
  private isPaginationOperation = false;
  private savedScrollPosition = 0;



  constructor() {
    effect(() => {
      if (this.libraryService.list().items.length > 0) {
        const newItems = this.libraryService.list().items;

        if (!this.data.length) {
          this.data = [...newItems];
        } else {
          const updatedData = [...this.data];

          const existingItemsMap = new Map(
            updatedData.map((item: any, index: number) => [item.id, index])
          );

          newItems.forEach((newItem: any) => {
            const existingIndex = existingItemsMap.get(newItem.id);

            if (existingIndex !== undefined) {
              updatedData[existingIndex] = newItem;
            } else {
              updatedData.push(newItem);
            }
          });

          this.data = updatedData;
        }
      }
    });
  }

  changeAudioFilter(e: Event) {
    this.data = [];
    this.filters.audio = (e.target as HTMLInputElement).checked;
    this.libraryService.applyFilters({...this.filters});
  }

  changePaidFilter(e: Event) {
    this.data = [];
    this.filters.paid = (e.target as HTMLInputElement).checked;
    this.libraryService.applyFilters({...this.filters});
  }

  applyFilter(loadMore: boolean = false) {
    this.data = [];

    this.filters = {
      ...this.filters,
      ...this.filter.value
    };

    this.libraryService.applyFilters({ ...this.filters });
  }


  openModal() {
    this.modal.nativeElement.showModal();
  }

  toggleSortDropdown() {
    this.dropdownSortOpen = !this.dropdownSortOpen;
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  selectSort(field: string) {
    if (this.currentSortField === field) {
      this.sortDirection = this.sortDirection === 'Asc' ? 'Desc' : 'Asc';
    } else {
      this.currentSortField = field;
      this.sortDirection = 'Asc';
    }

    const sortOrder = this.currentSortField + this.sortDirection;
    this.filter.patchValue({ sortOrder });
    this.filters.sortOrder = sortOrder;
    this.selectedSortLabel = this.sortOptions.find(option => option.value === field)?.label || '';

    // Sorting doesn't change the number of results, so don't show toast notification
    this.libraryService.applyFilters({...this.filters}, false);
  }

  ngOnInit() {
    const sortValue = this.filter.get('sortOrder')?.value || '';
    const match = sortValue.match(/^(.*?)(Asc|Desc)?$/);
    this.currentSortField = match?.[1] || 'title';
    this.sortDirection = (match?.[2] as 'Asc' | 'Desc') || 'Asc';
    this.selectedSortLabel = this.sortOptions.find(option => option.value === this.currentSortField)?.label || '';

    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    this.lang = this.translocoService.getActiveLang()

    // this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
    //   this.libraryService.getAll().subscribe()
    // })

    if (isPlatformBrowser(this.platformId)) {
      this.profileService.getProfile().subscribe({
        next: (profile) => {
          this.getUpdates();
        },
        error: () => {
          this.getUpdates();
        }
      });
    }



    this.searchSubject.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe((e) => {
      this.filters.description = e.target.value;
      this.applyFilter();
    });

    // Subscribe to filter results for toast notifications
    this.libraryService.filterResults$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(({total, isFilterOperation}) => {
      if (isFilterOperation) {
        const message = formatContentCountMessage(total, 'books');
        this.toasterService.showToast(message, 'default', 'bottom-middle', 4000);
      }
    });
  }

  getUpdates() {
    this.route.queryParams.pipe(
      takeUntilDestroyed(this.destroyRef),
      tap((params: any) => {
        // Initialize empty arrays for collection parameters
        this.filters.tags = [];
        this.filters.author = [];
        this.filters.format = [];

        //this.filter.get('sortOrder')?.setValue(params['sortOrder']);


        // Process query parameters and update filters
        Object.keys(params).forEach(key => {
          if (key === 'author' || key === 'format') {
            // Always convert to array, handling both string and array cases
            if (params[key]) {
              this.filters[key] = Array.isArray(params[key])
                ? params[key].map(String)  // Ensure all values are strings
                : [String(params[key])];   // Single value as array
            }
          } else if (key === 'page' || key === 'durationFrom' || key === 'durationTo') {
            // Convert numeric parameters
            this.filters[key] = Number(params[key]);
          } else {
            // Copy other parameters directly
            this.filters[key] = params[key];
          }
        });

        if (this.route.snapshot.data['video']) this.filters.youtube = 2;

        // Update internal filters but don't navigate to avoid loops
        this.libraryService.filters = { ...this.filters };

        // Update UI checkbox states
        this.updateTagCheckboxes();
        this.updateAuthorCheckboxes(); // Add this line
      }),
      switchMap(() => this.libraryService.getAll())
    ).subscribe();
  }

  transform(id: number, arr: { id: number }[], itemId = 'id'): boolean {
    if (!this.profileService.profile) {
      return false;
    }
    return arr.some((item: any) => item[itemId] === id);
  }

  getLangElement(item: any) {
    return item.translations.filter((book: any) => book.lang === this.lang)[0];
  }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/library/${content.code}`).then(() =>
        this.toasterService.showToast('Ссылка на книгу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }

  favorites(item: any) {
    if (!this.authService.isAuth) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.libraryService.addToFavourites(item.id).subscribe({
      next: (r) => {
        item.inFavourites = !item.inFavourites;
        if (!item.inFavourites) {
          this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Книга добавлена в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  like(item: any) {
    if (!this.authService.isAuth) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.libraryService.like(item.id).subscribe({
      next: (r) => {
        item.liked = !item.liked;
        if(item.liked) item.likes++
        else item.likes--;
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  setSortOrder(order: 'newFirst' | 'oldFirst' | 'maxViews' | 'minViews' | 'maxLikes' | 'minLikes' | 'maxDuration' | 'minDuration' | any) {
    this.filters.sortOrder = order.target?.value ? order.target.value : order;
    this.libraryService.applyFilters(this.filters);
  }

  changeTagFilter(tags: any) {
    this.data = [];
    const tagsCopy = tags && tags.length > 0
      ? tags.map((tag: any) => tag.id.toString())
      : [];

    this.filters.tags = tagsCopy;

    this.libraryService.applyFilters({ ...this.filters });
  }

  isLiked(item: any) {
    if (this.profileService.name() && this.profileService.profile) {
      return item.likes.some((e: any) => e.id == this.profileService?.profile?.id)
    }
  }

  // changeAudioFilter(e: any) {
  //   this.libraryService.applyFilters({...this.filters, audio: this.filters.audio ? 1 : 0})
  // }

  formatOptions = [
    { name: 'Наличие аудиокниги', value: 'Наличие аудиокниги', id: 0 }
  ]

  // Проверяет наличие аудио у книги
  hasAudio(item: any): boolean {
    const langElement = this.getLangElement(item);
    return langElement &&
           langElement.audio &&
           Array.isArray(langElement.audio) &&
           langElement.audio.length > 0;
  }

  changeAuthorFilter(authors: any) {
    this.data = [];
    console.log(authors);

    const tagsCopy = authors && authors.length > 0
      ? authors.map((tag: any) => tag.name)
      : [];

    this.filters.author = tagsCopy;

    this.libraryService.applyFilters({ ...this.filters });
  }

  changeCategoryFilter(categories: any) {
    this.data = [];

    const tagsCopy = categories && categories.length > 0
      ? categories.map((tag: any) => tag.name)
      : [];

    this.filters.category = tagsCopy;

    this.libraryService.applyFilters({ ...this.filters });
  }

  resetFilters() {
    for (let key in this.filters) this.filters[key] = '';
    this.filters.page = 1;
    this.libraryService.applyFilters(this.filters);
  }


  updateAuthorCheckboxes() {
    if (this.libraryService.list().authors && this.filters.author) {
      this.libraryService.list().authors.forEach((author: any) => {
        // Set checked property based on if the author name is in the filters
        author.checked = this.filters.author.includes(author.name);
      });
    }
  }

  // Add this method to explicitly update tag checkbox states based on filter values
  updateTagCheckboxes() {
    if (this.libraryService.list().tags && this.filters.tags) {
      this.libraryService.list().tags.forEach((tag: any) => {
        // Set checked property based on if the tag ID is in the filters
        tag.checked = this.filters.tags.includes(tag.id.toString());
      });
    }
  }


  showMenu(index: number, event: Event) {
    event.stopPropagation();

    this.show_menu = {};
    this.show_menu[index] = !this.show_menu[index];
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedElement = event.target as HTMLElement;

    const hasOpenMenu = Object.values(this.show_menu).some(isOpen => isOpen);

    if (hasOpenMenu) {
      const isMenuToggleClick = clickedElement.closest('.actions_w');

      if (!isMenuToggleClick) {
        this.show_menu = {};
      }
    }
  }

  nextPage() {
    if(this.filters.page < this.libraryService.totalPages) {
      this.filters.page++;
      this.libraryService.applyFilters(this.filters, false); // Don't show toast for pagination
    }
  }

  /**
   * Navigate to book detail page with audiobook tab active
   * @param item - The book item to navigate to
   * @param event - Click event to prevent propagation
   */
  navigateToAudiobook(item: any, event: Event) {
    event.stopPropagation();

    const lang = this.translocoService.getActiveLang();
    const bookCode = item.code;

    this.router.navigate([`/${lang}/library/${bookCode}`], {
      queryParams: { tab: 'Аудиокнига' }
    });
  }
}
